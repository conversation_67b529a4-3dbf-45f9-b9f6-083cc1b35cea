{"i18n-ally.localesPaths": ["assets/locales"], "rest-client.environmentVariables": {"$shared": {"development": "dev-api-pos.omos.tw", "production": "api-pos.omos.tw"}, "develop": {"scheme": "http", "host": "127.0.0.1:5000", "client": "omos", "channel": "<PERSON><PERSON><PERSON><PERSON>", "username": "um", "password": "a"}, "zhongxiao.歐魔帥集團": {"scheme": "https", "host": "dev-api-pos.omos.tw", "client": "omos", "channel": "<PERSON><PERSON><PERSON><PERSON>", "username": "um", "password": "a", "ch": "1", "bid": "1", "cid": "1"}, "ccabb.歐魔帥集團": {"scheme": "https", "host": "dev-api-pos.omos.tw", "client": "omos", "channel": "ccabb", "username": "um", "password": "a", "ch": "3", "bid": "1", "cid": "1"}, "ch.歐魔帥集團2": {"scheme": "https", "host": "dev-api-pos.omos.tw", "client": "omos2", "channel": "ch", "username": "um", "password": "a", "ch": "2", "cid": "2", "bid": "2"}, "Brede-Zhongshan.歐魔帥集團2": {"scheme": "https", "host": "dev-api-pos.omos.tw", "client": "omos2", "channel": "Brede-Zhongshan", "username": "um", "password": "a", "ch": "4", "cid": "2", "bid": "2"}, "跨界門市": {"scheme": "https", "host": "api-pos.omos.tw", "client": "omos", "channel": "12345678", "username": "12345678", "password": "12345678"}, "鴨肉香小吃店": {"scheme": "https", "host": "api-pos.omos.tw", "client": "19928296", "channel": "19928296", "username": "okshop83193989", "password": "A12345", "bid": "10", "cid": "9", "ch": "11"}, "羅馬時尚設計坊": {"scheme": "https", "host": "api-pos.omos.tw", "client": "85472542", "channel": "85472542", "username": "okshop83193989", "password": "A12345", "bid": "9", "cid": "8", "ch": "10"}}, "dart.flutterSdkPath": ".fvm/versions/1.22.6"}