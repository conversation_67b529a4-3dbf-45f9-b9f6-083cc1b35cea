import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/custom_scaffold.dart';
import 'package:guests/app/components/dialog_actions.dart';
import 'package:guests/app/components/future_progress.dart';
import 'package:guests/app/modules/account_detail/controllers/account_detail_controller.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/app/components/custom_editor.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';

class AccountDetailView extends GetView<AccountDetailController> {
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      clipped: false,
      title: Text(
        controller.displayTitle ?? '',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
      ),
      child: controller.data.obx(
        (state) {
          return Stack(
            children: [
              _main(),
              Align(
                alignment: Alignment.bottomCenter,
                child: BottomButton(
                  controller.displayButton,
                  onPressed: _submit,
                ),
              ),
            ],
          );
        },
        onError: (error) => Blank(),
      ),
    );
  }

  Future<void> _submit() async {
    final state = controller.gKey.currentState;
    if (state != null && state.validate()) {
      final res = await FutureProgress.show(future: controller.submit());
      if (res is num) {
        Get.back();
      }
    }
  }

  Iterable<Widget> _children() sync* {
    // 編輯帳號才需要顯示啟用帳號
    if (controller.isUpdating) {
      yield ColoredBox(
        color: Colors.white,
        child: SwitchListTile(
          contentPadding: kContentPadding,
          title: Text(
            '啟用帳號', // TODO: i18n
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textAlign: TextAlign.left,
          ),
          value: this.controller.enabled,
          onChanged: (value) {
            this.controller.enabled = value;
          },
        ),
      );
    }
    if (controller.enabled) {
      yield* _page1();
      // yield* _page2();
      yield* _page3();
      yield* _page4();
    }
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text(
        '請填寫操作員資料', // TODO: i18n
        style: const TextStyle(
          fontSize: 16,
          color: kColorPrimary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        vertical: 12.0,
      ),
    );
    children.addIf(
      true,
      Expanded(
        child: Form(
          key: controller.gKey,
          child: Obx(() {
            return ListView(
              padding: EdgeInsets.only(bottom: kBottomPadding),
              children: _children().toList(growable: false),
            );
          }),
        ),
      ),
    );
    return Column(children: children);
  }

  Iterable<Widget> _page1() sync* {
    yield Text(
      '帳號資訊', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield CustomEditor(
      initialValue: this.controller.data.value.name,
      labelText: '名稱', // TODO: i18n
      hintText: '請輸入操作員名稱', // TODO: i18n
      onChanged: (value) {
        this.controller.data.value.name = value;
      },
    );
    yield CustomEditor(
      // readonly: this.controller.isUpdating,
      enable: this.controller.isCreating,
      initialValue: this.controller.data.value.username,
      labelText: '帳號', // TODO: i18n
      hintText: '請輸入帳號', // TODO: i18n
      onChanged: (value) {
        this.controller.data.value.username = value;
      },
      validator: (value) {
        value ??= '';
        if (value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '密碼', // TODO: i18n
      hintText: '請輸入密碼', // TODO: i18n
      onChanged: (value) {
        controller.password.value = value;
      },
      validator: (value) {
        value ??= '';
        if (value.isEmpty && controller.isCreating) {
          return '必填項目';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '再次輸入密碼', // TODO: i18n
      hintText: '請輸入密碼', // TODO: i18n
      onChanged: (value) {
        controller.confirmPassword.value = value;
      },
      validator: (value) {
        if (this.controller.password.value !=
            this.controller.confirmPassword.value) {
          return '確認密碼不同'; // TODO: i18n
        }
        return null;
      },
    );
    yield Container(
      color: Colors.white,
      height: 8.0,
    );
  }

  Iterable<Widget> _page2() sync* {
    yield Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: kPadding,
        vertical: 8.0,
      ),
      child: Text(
        '取貨點(可複選)',
        style: TextStyle(
          fontSize: 14,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.left,
      ),
    );
    for (var i = 0; i < 4; i++) {
      yield ObxValue<RxBool>((data) {
        return ListTile(
          tileColor: Colors.white,
          title: Text(
            '取貨點$i',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black,
              height: 2.5,
            ),
            textAlign: TextAlign.left,
          ),
          trailing: IconButton(
            icon: Icon(
              data.value ? Icons.check_circle : Icons.radio_button_off,
              color: data.value ? kColorPrimary : Colors.grey,
            ),
            onPressed: () => data.toggle(),
          ),
        );
      }, false.obs);
    }
  }

  Iterable<Widget> _page3() sync* {
    yield Text(
      '身份',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield ListTile(
      onTap: _showDialog,
      tileColor: Colors.white,
      title: Obx(() {
        kLogger.d('${controller.roleId}');
        return Text(
          this.controller.data.value.displayRoleId,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        );
      }),
      trailing: const IconButton(
        icon: Icon(Icons.expand_more),
        onPressed: null,
      ),
    );
  }

  ///
  /// 選擇身份
  ///
  Future<void> _showDialog() async {
    final index = await DialogActions.show<num>(
      titleText: '請選擇身份', // TODO: i18n
      actions: <String>[
        StoreRole.Boss.name,
        StoreRole.Employee.name,
      ],
    );
    if (index == 0) {
      controller.roleId = StoreRole.Boss.index;
    } else if (index == 1) {
      controller.roleId = StoreRole.Employee.index;
    }
  }

  Iterable<Widget> _page4() sync* {
    yield Text(
      '備註', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield ColoredBox(
      color: Colors.white,
      child: TextFormField(
        initialValue: this.controller.data.value.comment,
        onChanged: (value) {
          this.controller.data.value.comment = value;
        },
        minLines: 3,
        keyboardType: TextInputType.multiline,
        maxLines: null,
        decoration: const InputDecoration(
          hintText: '請輸入備註…', // TODO: i18n
          border: const OutlineInputBorder(
            borderRadius: BorderRadius.zero,
          ),
          hintStyle: const TextStyle(
            fontSize: 16,
            color: const Color(0xff666666),
          ),
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 16.0,
      ),
    );
  }
}
