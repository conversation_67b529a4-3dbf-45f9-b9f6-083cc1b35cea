import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/extension.dart';

class AccountDetailController extends GetxController {
  final _disposed = Completer();
  final AccountProvider accountProvider;
  final _enabled = false.obs;
  final _id = ''.obs;
  final password = ''.obs;
  final confirmPassword = ''.obs;
  final _roleId = 0.obs;
  final data = Value<StoreAccount>(null);
  final gKey = GlobalKey<FormState>();

  String get id => _id.value;
  bool get isCreating => _id.value.isEmpty;
  bool get isUpdating => _id.value.isNotEmpty;
  StoreAccount get draft => data.value;
  String get displayButton => isCreating ? '新增' : '修改';
  String get displayTitle => '$displayButton操作員';

  bool get enabled {
    this.data.value.status ??= 1;
    final ret = this.data.value.status != 0;
    if (this._enabled.value != ret) {
      this._enabled.value = ret;
    }
    return ret;
  }

  set enabled(bool value) {
    this.data.value.status = value ? 1 : 0;
    // super.refresh();
    this._enabled.value = value;
  }

  num get roleId {
    final ret = this.data.value.roleId;
    if (this._roleId.value != ret) {
      this._roleId.value = ret;
    }
    return ret;
  }

  set roleId(num value) {
    this.data.value.roleId = value;
    this._roleId.value = value;
  }

  AccountDetailController({
    @required this.accountProvider,
  });

  void onPostFrame() {
    //
  }

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asyncMapSample((event) => onRefresh())
        .takeUntil(_disposed.future)
        .listen((value) {
      data.append(() => () async => data.value);
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey('id')) {
      _id.value = Get.parameters['id'];
    } else {
      _id.value = '';
    }
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    if (id != null && id.isNotEmpty) {
      data.value = await accountProvider.getStoreAccount(id);
    } else {
      data.value = StoreAccount();
    }
  }

  Future<num> _update() {
    final req = data.value.toUpdateStoreAccountReq();
    req.password = password.value;
    return accountProvider.updateStoreAccount(id, req);
  }

  Future<num> _create() {
    final req = data.value.toCreateStoreAccountReq();
    req.password = password.value;
    return accountProvider.createStoreAccount(req);
  }

  Future<num> submit() async {
    final future = isCreating ? _create : _update;
    return future();
  }
}
