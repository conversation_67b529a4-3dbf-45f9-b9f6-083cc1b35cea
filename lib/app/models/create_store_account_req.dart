// To parse this JSON data, do
//
//     final createStoreAccountReq = createStoreAccountReqFromJson(jsonString);

import 'dart:convert';

class CreateStoreAccountReq {
  CreateStoreAccountReq({
    this.roleId,
    this.username,
    this.name,
    this.password,
    this.status,
    this.comment,
  });

  num roleId;
  String username;
  String name;
  String password;
  num status;
  String comment;

  CreateStoreAccountReq copyWith({
    num roleId,
    String username,
    String name,
    String password,
    num status,
    String comment,
  }) =>
      CreateStoreAccountReq(
        roleId: roleId ?? this.roleId,
        username: username ?? this.username,
        name: name ?? this.name,
        password: password ?? this.password,
        status: status ?? this.status,
        comment: comment ?? this.comment,
      );

  factory CreateStoreAccountReq.fromRawJson(String str) =>
      CreateStoreAccountReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CreateStoreAccountReq.fromJson(Map<String, dynamic> json) =>
      CreateStoreAccountReq(
        roleId: json["role_id"],
        username: json["username"],
        name: json["name"],
        password: json["password"],
        status: json["status"],
        comment: json["comment"],
      );

  Map<String, dynamic> toJson() => {
        "role_id": roleId,
        "username": username,
        "name": name,
        "password": password,
        "status": status,
        "comment": comment,
      };
}
