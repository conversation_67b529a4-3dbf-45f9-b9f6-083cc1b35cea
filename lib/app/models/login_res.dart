// To parse this JSON data, do
//
//     final login = login<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';

class LoginRes {
  LoginRes({
    this.alreadyLogin,
    this.token,
  });

  bool alreadyLogin;
  String token;

  LoginRes copyWith({
    bool alreadyLogin,
    String token,
  }) =>
      LoginRes(
        alreadyLogin: alreadyLogin ?? this.alreadyLogin,
        token: token ?? this.token,
      );

  factory LoginRes.fromRawJson(String str) =>
      LoginRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginRes.fromJson(Map<String, dynamic> json) => LoginRes(
        alreadyLogin: json["already_login"],
        token: json["token"],
      );

  Map<String, dynamic> toJson() => {
        "already_login": alreadyLogin,
        "token": token,
      };
}
