// To parse this JSON data, do
//
//     final orderCreating = orderCreatingFromJson(jsonString);

import 'dart:convert';

class OrderCreating {
  OrderCreating({
    this.memberId,
    this.carrierType,
    this.discount,
    this.carrierId,
    this.subtotal,
    this.total,
    this.redeemMemberPoints,
    this.randomNumber,
    this.additionalCharges,
    this.pointGet,
    this.createdAt,
    this.pointDiscountLimit,
    this.paid,
    this.invoice,
    this.memberCouponId,
    this.change,
    this.updatedAt,
    this.productPrice,
    this.couponDiscount,
    this.invoicePaper,
    this.invoiceNumber,
    this.npoBan,
    this.vatNumber,
  });

  num memberId;
  num carrierType;
  num discount;
  String carrierId;
  num subtotal;
  num total;
  num redeemMemberPoints;
  String randomNumber;
  num additionalCharges;
  num pointGet;
  String createdAt;
  num pointDiscountLimit;
  num paid;
  bool invoice;
  num memberCouponId;
  num change;
  String updatedAt;
  num productPrice;
  num couponDiscount;
  bool invoicePaper;
  String invoiceNumber;
  String npoBan;
  String vatNumber;

  OrderCreating copyWith({
    num memberId,
    num carrierType,
    num discount,
    String carrierId,
    num subtotal,
    num total,
    num redeemMemberPoints,
    String randomNumber,
    num additionalCharges,
    num pointGet,
    String createdAt,
    num pointDiscountLimit,
    num paid,
    bool invoice,
    num memberCouponId,
    num change,
    String updatedAt,
    num productPrice,
    num couponDiscount,
    bool invoicePaper,
    String invoiceNumber,
    String npoBan,
    String vatNumber,
  }) =>
      OrderCreating(
        memberId: memberId ?? this.memberId,
        carrierType: carrierType ?? this.carrierType,
        discount: discount ?? this.discount,
        carrierId: carrierId ?? this.carrierId,
        subtotal: subtotal ?? this.subtotal,
        total: total ?? this.total,
        redeemMemberPoints: redeemMemberPoints ?? this.redeemMemberPoints,
        randomNumber: randomNumber ?? this.randomNumber,
        additionalCharges: additionalCharges ?? this.additionalCharges,
        pointGet: pointGet ?? this.pointGet,
        createdAt: createdAt ?? this.createdAt,
        pointDiscountLimit: pointDiscountLimit ?? this.pointDiscountLimit,
        paid: paid ?? this.paid,
        invoice: invoice ?? this.invoice,
        memberCouponId: memberCouponId ?? this.memberCouponId,
        change: change ?? this.change,
        updatedAt: updatedAt ?? this.updatedAt,
        productPrice: productPrice ?? this.productPrice,
        couponDiscount: couponDiscount ?? this.couponDiscount,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        npoBan: npoBan ?? this.npoBan,
        vatNumber: vatNumber ?? this.vatNumber,
      );

  factory OrderCreating.fromRawJson(String str) =>
      OrderCreating.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderCreating.fromJson(Map<String, dynamic> json) => OrderCreating(
        memberId: json["member_id"],
        carrierType: json["carrier_type"],
        discount: json["discount"],
        carrierId: json["carrier_id"],
        subtotal: json["subtotal"],
        total: json["total"],
        redeemMemberPoints: json["redeem_member_points"],
        randomNumber: json["random_number"],
        additionalCharges: json["additional_charges"],
        pointGet: json["point_get"],
        createdAt: json["created_at"],
        pointDiscountLimit: json["point_discount_limit"],
        paid: json["paid"],
        invoice: json["invoice"],
        memberCouponId: json["member_coupon_id"],
        change: json["change"],
        updatedAt: json["updated_at"],
        productPrice: json["product_price"],
        couponDiscount: json["coupon_discount"],
        invoicePaper: json["invoice_paper"],
        invoiceNumber: json["invoice_number"],
        npoBan: json["npo_ban"],
        vatNumber: json["vat_number"],
      );

  Map<String, dynamic> toJson() => {
        "member_id": memberId,
        "carrier_type": carrierType,
        "discount": discount,
        "carrier_id": carrierId,
        "subtotal": subtotal,
        "total": total,
        "redeem_member_points": redeemMemberPoints,
        "random_number": randomNumber,
        "additional_charges": additionalCharges,
        "point_get": pointGet,
        "created_at": createdAt,
        "point_discount_limit": pointDiscountLimit,
        "paid": paid,
        "invoice": invoice,
        "member_coupon_id": memberCouponId,
        "change": change,
        "updated_at": updatedAt,
        "product_price": productPrice,
        "coupon_discount": couponDiscount,
        "invoice_paper": invoicePaper,
        "invoice_number": invoiceNumber,
        "npo_ban": npoBan,
        "vat_number": vatNumber,
      };
}
