// To parse this JSON data, do
//
//     final errorMessage = errorMessageFromJson(jsonString);

import 'dart:convert';

class ErrorMessage {
  ErrorMessage({
    this.message,
    this.code,
    this.request,
  });

  String message;
  num code;
  String request;

  ErrorMessage copyWith({
    String message,
    num code,
    String request,
  }) =>
      ErrorMessage(
        message: message ?? this.message,
        code: code ?? this.code,
        request: request ?? this.request,
      );

  factory ErrorMessage.fromRawJson(String str) =>
      ErrorMessage.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErrorMessage.fromJson(Map<String, dynamic> json) => ErrorMessage(
        message: json["message"],
        code: json["code"],
        request: json["request"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "code": code,
        "request": request,
      };
}
