// To parse this JSON data, do
//
//     final orderCreated = orderCreatedFromJson(jsonString);

import 'dart:convert';

class OrderCreated {
  OrderCreated({
    this.isCreated,
    this.orderId,
    this.promotionCoupons,
  });

  bool isCreated;
  num orderId;
  List<PromotionCoupon> promotionCoupons;

  OrderCreated copyWith({
    bool isCreated,
    num orderId,
    List<PromotionCoupon> promotionCoupons,
  }) =>
      OrderCreated(
        isCreated: isCreated ?? this.isCreated,
        orderId: orderId ?? this.orderId,
        promotionCoupons: promotionCoupons ?? this.promotionCoupons,
      );

  factory OrderCreated.fromRawJson(String str) =>
      OrderCreated.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderCreated.fromJson(Map<String, dynamic> json) => OrderCreated(
        isCreated: json["is_created"],
        orderId: json["order_id"],
        promotionCoupons: List<PromotionCoupon>.from(
            json["promotion_coupons"].map((x) => PromotionCoupon.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "is_created": isCreated,
        "order_id": orderId,
        "promotion_coupons":
            List<dynamic>.from(promotionCoupons.map((x) => x.toJson())),
      };
}

class PromotionCoupon {
  PromotionCoupon({
    this.title,
  });

  String title;

  PromotionCoupon copyWith({
    String title,
  }) =>
      PromotionCoupon(
        title: title ?? this.title,
      );

  factory PromotionCoupon.fromRawJson(String str) =>
      PromotionCoupon.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromotionCoupon.fromJson(Map<String, dynamic> json) =>
      PromotionCoupon(
        title: json["title"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
      };
}
