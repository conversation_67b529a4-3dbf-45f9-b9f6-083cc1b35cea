// To parse this JSON data, do
//
//     final clientInfo = clientInfoFromJson(jsonString);

import 'dart:convert';

class ClientInfo {
  ClientInfo({
    this.id,
    this.groupName,
    this.groupCode,
    this.contactName,
    this.contactPhone,
    this.contactEmail,
    this.createdAt,
    this.updatedAt,
  });

  num id;
  String groupName;
  String groupCode;
  String contactName;
  String contactPhone;
  String contactEmail;
  String createdAt;
  String updatedAt;

  ClientInfo copyWith({
    num id,
    String groupName,
    String groupCode,
    String contactName,
    String contactPhone,
    String contactEmail,
    String createdAt,
    String updatedAt,
  }) =>
      ClientInfo(
        id: id ?? this.id,
        groupName: groupName ?? this.groupName,
        groupCode: groupCode ?? this.groupCode,
        contactName: contactName ?? this.contactName,
        contactPhone: contactPhone ?? this.contactPhone,
        contactEmail: contactEmail ?? this.contactEmail,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory ClientInfo.fromRawJson(String str) =>
      ClientInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ClientInfo.fromJson(Map<String, dynamic> json) => ClientInfo(
        id: json["id"],
        groupName: json["group_name"],
        groupCode: json["group_code"],
        contactName: json["contact_name"],
        contactPhone: json["contact_phone"],
        contactEmail: json["contact_email"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "group_name": groupName,
        "group_code": groupCode,
        "contact_name": contactName,
        "contact_phone": contactPhone,
        "contact_email": contactEmail,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
