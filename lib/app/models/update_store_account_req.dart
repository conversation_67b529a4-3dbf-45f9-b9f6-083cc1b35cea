// To parse this JSON data, do
//
//     final updateStoreAccountReq = updateStoreAccountReqFromJson(jsonString);

import 'dart:convert';

class UpdateStoreAccountReq {
  UpdateStoreAccountReq({
    this.roleId,
    this.name,
    this.status,
    this.comment,
    this.password,
  });

  num roleId;
  String name;
  num status;
  String comment;
  String password;

  UpdateStoreAccountReq copyWith({
    num roleId,
    String name,
    num status,
    String comment,
    String password,
  }) =>
      UpdateStoreAccountReq(
        roleId: roleId ?? this.roleId,
        name: name ?? this.name,
        status: status ?? this.status,
        comment: comment ?? this.comment,
        password: password ?? this.password,
      );

  factory UpdateStoreAccountReq.fromRawJson(String str) =>
      UpdateStoreAccountReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UpdateStoreAccountReq.fromJson(Map<String, dynamic> json) =>
      UpdateStoreAccountReq(
        roleId: json["role_id"],
        name: json["name"],
        status: json["status"],
        comment: json["comment"],
        password: json["password"],
      );

  Map<String, dynamic> toJson() => {
        "role_id": roleId,
        "name": name,
        "status": status,
        "comment": comment,
        "password": password,
      };
}
