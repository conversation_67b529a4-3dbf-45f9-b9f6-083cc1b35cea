// To parse this JSON data, do
//
//     final invoiceModel = invoiceModelFromJson(jsonString);

import 'dart:convert';

class InvoiceModel {
  InvoiceModel({
    this.invoiceNumber,
    this.invoiceDate,
    this.randomNumber,
    this.totalAmount,
    this.buyerIdentifier,
    this.sellerIdentifier,
    this.businessIdentifier,
    this.productArray,
    this.printMark,
    this.productName,
    this.storeName,
  });

  String invoiceNumber;
  num invoiceDate;
  String randomNumber;
  num totalAmount;
  String buyerIdentifier;
  String sellerIdentifier;
  String businessIdentifier;
  String productArray;
  num printMark;
  String productName;
  String storeName;

  InvoiceModel copyWith({
    String invoiceNumber,
    num invoiceDate,
    String randomNumber,
    num totalAmount,
    String buyerIdentifier,
    String sellerIdentifier,
    String businessIdentifier,
    String productArray,
    num printMark,
    String productName,
    String storeName,
  }) =>
      InvoiceModel(
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        invoiceDate: invoiceDate ?? this.invoiceDate,
        randomNumber: randomNumber ?? this.randomNumber,
        totalAmount: totalAmount ?? this.totalAmount,
        buyerIdentifier: buyerIdentifier ?? this.buyerIdentifier,
        sellerIdentifier: sellerIdentifier ?? this.sellerIdentifier,
        businessIdentifier: businessIdentifier ?? this.businessIdentifier,
        productArray: productArray ?? this.productArray,
        printMark: printMark ?? this.printMark,
        productName: productName ?? this.productName,
        storeName: storeName ?? this.storeName,
      );

  factory InvoiceModel.fromRawJson(String str) =>
      InvoiceModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvoiceModel.fromJson(Map<String, dynamic> json) => InvoiceModel(
        invoiceNumber: json["InvoiceNumber"],
        invoiceDate: json["InvoiceDate"],
        randomNumber: json["RandomNumber"],
        totalAmount: json["TotalAmount"],
        buyerIdentifier: json["BuyerIdentifier"],
        sellerIdentifier: json["SellerIdentifier"],
        businessIdentifier: json["BusinessIdentifier"],
        productArray: json["productArray"],
        printMark: json["printMark"],
        productName: json["productName"],
        storeName: json["storeName"],
      );

  Map<String, dynamic> toJson() => {
        "InvoiceNumber": invoiceNumber,
        "InvoiceDate": invoiceDate,
        "RandomNumber": randomNumber,
        "TotalAmount": totalAmount,
        "BuyerIdentifier": buyerIdentifier,
        "SellerIdentifier": sellerIdentifier,
        "BusinessIdentifier": businessIdentifier,
        "productArray": productArray,
        "printMark": printMark,
        "productName": productName,
        "storeName": storeName,
      };
}
