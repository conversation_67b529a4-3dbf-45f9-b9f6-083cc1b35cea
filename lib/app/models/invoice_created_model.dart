// To parse this JSON data, do
//
//     final invoiceCreated = invoiceCreatedFrom<PERSON>son(jsonString);

import 'dart:convert';

class InvoiceCreated {
  InvoiceCreated({
    this.invoiceNo,
    this.status,
    this.message,
  });

  String invoiceNo;
  String status;
  String message;

  InvoiceCreated copyWith({
    String invoiceNo,
    String status,
    String message,
  }) =>
      InvoiceCreated(
        invoiceNo: invoiceNo ?? this.invoiceNo,
        status: status ?? this.status,
        message: message ?? this.message,
      );

  factory InvoiceCreated.fromRawJson(String str) =>
      InvoiceCreated.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvoiceCreated.fromJson(Map<String, dynamic> json) => InvoiceCreated(
        invoiceNo: json["InvoiceNo"],
        status: json["Status"],
        message: json["Message"],
      );

  Map<String, dynamic> toJson() => {
        "InvoiceNo": invoiceNo,
        "Status": status,
        "Message": message,
      };
}
