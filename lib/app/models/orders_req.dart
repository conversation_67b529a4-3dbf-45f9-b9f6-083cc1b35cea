// To parse this JSON data, do
//
//     final ordersReq = ordersReqFromJson(jsonString);

import 'dart:convert';

class OrdersReq {
  OrdersReq({
    this.page,
    this.limit,
    this.status,
    this.keyword,
  });

  num page;
  num limit;
  num status;
  String keyword;

  OrdersReq copyWith({
    num page,
    num limit,
    num status,
    String keyword,
  }) =>
      OrdersReq(
        page: page ?? this.page,
        limit: limit ?? this.limit,
        status: status ?? this.status,
        keyword: keyword ?? this.keyword,
      );

  factory OrdersReq.fromRawJson(String str) =>
      OrdersReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersReq.fromJson(Map<String, dynamic> json) => OrdersReq(
        page: json["page"],
        limit: json["limit"],
        status: json["status"],
        keyword: json["keyword"],
      );

  Map<String, dynamic> toJson() => {
        "page": page,
        "limit": limit,
        "status": status,
        "keyword": keyword,
      };
}
