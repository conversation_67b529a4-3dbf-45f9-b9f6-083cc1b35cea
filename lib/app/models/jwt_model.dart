// To parse this JSON data, do
//
//     final jwt = jwtFromJson(jsonString);

import 'dart:convert';

class Jwt {
  Jwt({
    this.brandId,
    this.name,
    this.id,
    this.role,
    this.clientId,
    this.channelId,
    this.timezone,
    this.lastLogin,
    this.sysType,
    this.tokenType,
    this.iat,
    this.exp,
  });

  num brandId;
  String name;
  num id;
  Role role;
  num clientId;
  num channelId;
  String timezone;
  String lastLogin;
  String sysType;
  String tokenType;
  num iat;
  num exp;

  Jwt copyWith({
    num brandId,
    String name,
    num id,
    Role role,
    num clientId,
    num channelId,
    String timezone,
    String lastLogin,
    String sysType,
    String tokenType,
    num iat,
    num exp,
  }) =>
      Jwt(
        brandId: brandId ?? this.brandId,
        name: name ?? this.name,
        id: id ?? this.id,
        role: role ?? this.role,
        clientId: clientId ?? this.clientId,
        channelId: channelId ?? this.channelId,
        timezone: timezone ?? this.timezone,
        lastLogin: lastLogin ?? this.lastLogin,
        sysType: sysType ?? this.sysType,
        tokenType: tokenType ?? this.tokenType,
        iat: iat ?? this.iat,
        exp: exp ?? this.exp,
      );

  factory Jwt.fromRawJson(String str) => Jwt.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Jwt.fromJson(Map<String, dynamic> json) => Jwt(
        brandId: json["brand_id"] == null ? null : json["brand_id"],
        name: json["name"] == null ? null : json["name"],
        id: json["id"] == null ? null : json["id"],
        role: json["role"] == null ? null : Role.fromJson(json["role"]),
        clientId: json["client_id"] == null ? null : json["client_id"],
        channelId: json["channel_id"] == null ? null : json["channel_id"],
        timezone: json["timezone"] == null ? null : json["timezone"],
        lastLogin: json["last_login"] == null ? null : json["last_login"],
        sysType: json["sys_type"] == null ? null : json["sys_type"],
        tokenType: json["token_type"] == null ? null : json["token_type"],
        iat: json["iat"] == null ? null : json["iat"],
        exp: json["exp"] == null ? null : json["exp"],
      );

  Map<String, dynamic> toJson() => {
        "brand_id": brandId == null ? null : brandId,
        "name": name == null ? null : name,
        "id": id == null ? null : id,
        "role": role == null ? null : role.toJson(),
        "client_id": clientId == null ? null : clientId,
        "channel_id": channelId == null ? null : channelId,
        "timezone": timezone == null ? null : timezone,
        "last_login": lastLogin == null ? null : lastLogin,
        "sys_type": sysType == null ? null : sysType,
        "token_type": tokenType == null ? null : tokenType,
        "iat": iat == null ? null : iat,
        "exp": exp == null ? null : exp,
      };
}

class Role {
  Role({
    this.name,
    this.id,
  });

  String name;
  num id;

  Role copyWith({
    String name,
    num id,
  }) =>
      Role(
        name: name ?? this.name,
        id: id ?? this.id,
      );

  factory Role.fromRawJson(String str) => Role.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        name: json["name"] == null ? null : json["name"],
        id: json["id"] == null ? null : json["id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name == null ? null : name,
        "id": id == null ? null : id,
      };
}
