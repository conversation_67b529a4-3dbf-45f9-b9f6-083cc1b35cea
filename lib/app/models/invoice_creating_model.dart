// To parse this JSON data, do
//
//     final invoiceCreating = invoiceCreatingFromJson(jsonString);

import 'dart:convert';

InvoiceCreating invoiceCreatingFromJson(String str) => InvoiceCreating.fromJson(json.decode(str));

String invoiceCreatingToJson(InvoiceCreating data) => json.encode(data.toJson());

class InvoiceCreating {
    InvoiceCreating({
        this.posBan,
        this.apiKey,
        this.state,
        this.sellerBan,
        this.storeCode,
        this.storeName,
        this.registerCode,
        this.orderNo,
        this.invoiceNo,
        this.invoiceDate,
        this.allowanceDate,
        this.buyerBan,
        this.printMark,
        this.memberId,
        this.checkNo,
        this.invoiceType,
        this.groupMark,
        this.salesAmt,
        this.freeTaxSalesAmt,
        this.zeroTaxSalesAmt,
        this.taxAmt,
        this.totalAmt,
        this.taxType,
        this.taxRate,
        this.discountAmt,
        this.healthyAmt,
        this.carrierType,
        this.carrierId1,
        this.carrierId2,
        this.npoBan,
        this.randomNumber,
        this.mainRemark,
        this.formatType,
        this.invoiceDetails,
    });

    String posBan;
    String apiKey;
    num state;
    String sellerBan;
    String storeCode;
    String storeName;
    String registerCode;
    String orderNo;
    String invoiceNo;
    String invoiceDate;
    String allowanceDate;
    String buyerBan;
    String printMark;
    String memberId;
    String checkNo;
    String invoiceType;
    String groupMark;
    num salesAmt;
    num freeTaxSalesAmt;
    num zeroTaxSalesAmt;
    num taxAmt;
    num totalAmt;
    String taxType;
    num taxRate;
    num discountAmt;
    num healthyAmt;
    String carrierType;
    String carrierId1;
    String carrierId2;
    String npoBan;
    String randomNumber;
    String mainRemark;
    String formatType;
    List<InvoiceDetail> invoiceDetails;

    factory InvoiceCreating.fromJson(Map<String, dynamic> json) => InvoiceCreating(
        posBan: json["PosBAN"] == null ? null : json["PosBAN"],
        apiKey: json["ApiKey"] == null ? null : json["ApiKey"],
        state: json["State"] == null ? null : json["State"],
        sellerBan: json["SellerBAN"] == null ? null : json["SellerBAN"],
        storeCode: json["StoreCode"] == null ? null : json["StoreCode"],
        storeName: json["StoreName"] == null ? null : json["StoreName"],
        registerCode: json["RegisterCode"] == null ? null : json["RegisterCode"],
        orderNo: json["OrderNo"] == null ? null : json["OrderNo"],
        invoiceNo: json["InvoiceNo"] == null ? null : json["InvoiceNo"],
        invoiceDate: json["InvoiceDate"] == null ? null : json["InvoiceDate"],
        allowanceDate: json["AllowanceDate"] == null ? null : json["AllowanceDate"],
        buyerBan: json["BuyerBAN"] == null ? null : json["BuyerBAN"],
        printMark: json["PrintMark"] == null ? null : json["PrintMark"],
        memberId: json["MemberId"] == null ? null : json["MemberId"],
        checkNo: json["CheckNo"] == null ? null : json["CheckNo"],
        invoiceType: json["InvoiceType"] == null ? null : json["InvoiceType"],
        groupMark: json["GroupMark"] == null ? null : json["GroupMark"],
        salesAmt: json["SalesAmt"] == null ? null : json["SalesAmt"],
        freeTaxSalesAmt: json["FreeTaxSalesAmt"] == null ? null : json["FreeTaxSalesAmt"],
        zeroTaxSalesAmt: json["ZeroTaxSalesAmt"] == null ? null : json["ZeroTaxSalesAmt"],
        taxAmt: json["TaxAmt"] == null ? null : json["TaxAmt"],
        totalAmt: json["TotalAmt"] == null ? null : json["TotalAmt"],
        taxType: json["TaxType"] == null ? null : json["TaxType"],
        taxRate: json["TaxRate"] == null ? null : json["TaxRate"].toDouble(),
        discountAmt: json["DiscountAmt"] == null ? null : json["DiscountAmt"],
        healthyAmt: json["HealthyAmt"] == null ? null : json["HealthyAmt"],
        carrierType: json["CarrierType"] == null ? null : json["CarrierType"],
        carrierId1: json["CarrierId1"] == null ? null : json["CarrierId1"],
        carrierId2: json["CarrierId2"] == null ? null : json["CarrierId2"],
        npoBan: json["NpoBan"] == null ? null : json["NpoBan"],
        randomNumber: json["RandomNumber"] == null ? null : json["RandomNumber"],
        mainRemark: json["MainRemark"] == null ? null : json["MainRemark"],
        formatType: json["FormatType"] == null ? null : json["FormatType"],
        invoiceDetails: json["InvoiceDetails"] == null ? null : List<InvoiceDetail>.from(json["InvoiceDetails"].map((x) => InvoiceDetail.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "PosBAN": posBan == null ? null : posBan,
        "ApiKey": apiKey == null ? null : apiKey,
        "State": state == null ? null : state,
        "SellerBAN": sellerBan == null ? null : sellerBan,
        "StoreCode": storeCode == null ? null : storeCode,
        "StoreName": storeName == null ? null : storeName,
        "RegisterCode": registerCode == null ? null : registerCode,
        "OrderNo": orderNo == null ? null : orderNo,
        "InvoiceNo": invoiceNo == null ? null : invoiceNo,
        "InvoiceDate": invoiceDate == null ? null : invoiceDate,
        "AllowanceDate": allowanceDate == null ? null : allowanceDate,
        "BuyerBAN": buyerBan == null ? null : buyerBan,
        "PrintMark": printMark == null ? null : printMark,
        "MemberId": memberId == null ? null : memberId,
        "CheckNo": checkNo == null ? null : checkNo,
        "InvoiceType": invoiceType == null ? null : invoiceType,
        "GroupMark": groupMark == null ? null : groupMark,
        "SalesAmt": salesAmt == null ? null : salesAmt,
        "FreeTaxSalesAmt": freeTaxSalesAmt == null ? null : freeTaxSalesAmt,
        "ZeroTaxSalesAmt": zeroTaxSalesAmt == null ? null : zeroTaxSalesAmt,
        "TaxAmt": taxAmt == null ? null : taxAmt,
        "TotalAmt": totalAmt == null ? null : totalAmt,
        "TaxType": taxType == null ? null : taxType,
        "TaxRate": taxRate == null ? null : taxRate,
        "DiscountAmt": discountAmt == null ? null : discountAmt,
        "HealthyAmt": healthyAmt == null ? null : healthyAmt,
        "CarrierType": carrierType == null ? null : carrierType,
        "CarrierId1": carrierId1 == null ? null : carrierId1,
        "CarrierId2": carrierId2 == null ? null : carrierId2,
        "NpoBan": npoBan == null ? null : npoBan,
        "RandomNumber": randomNumber == null ? null : randomNumber,
        "MainRemark": mainRemark == null ? null : mainRemark,
        "FormatType": formatType == null ? null : formatType,
        "InvoiceDetails": invoiceDetails == null ? null : List<dynamic>.from(invoiceDetails.map((x) => x.toJson())),
    };
}

class InvoiceDetail {
    InvoiceDetail({
        this.sequenceNo,
        this.itemName,
        this.qty,
        this.unit,
        this.unitPrice,
        this.salesAmt,
        this.taxAmt,
        this.totalAmt,
        this.discountAmt,
        this.healthAmt,
        this.relateNumber,
        this.remark,
    });

    String sequenceNo;
    String itemName;
    num qty;
    String unit;
    num unitPrice;
    num salesAmt;
    num taxAmt;
    num totalAmt;
    num discountAmt;
    num healthAmt;
    String relateNumber;
    String remark;

    factory InvoiceDetail.fromJson(Map<String, dynamic> json) => InvoiceDetail(
        sequenceNo: json["SequenceNo"] == null ? null : json["SequenceNo"],
        itemName: json["ItemName"] == null ? null : json["ItemName"],
        qty: json["Qty"] == null ? null : json["Qty"],
        unit: json["Unit"] == null ? null : json["Unit"],
        unitPrice: json["UnitPrice"] == null ? null : json["UnitPrice"],
        salesAmt: json["SalesAmt"] == null ? null : json["SalesAmt"],
        taxAmt: json["TaxAmt"] == null ? null : json["TaxAmt"],
        totalAmt: json["TotalAmt"] == null ? null : json["TotalAmt"],
        discountAmt: json["DiscountAmt"] == null ? null : json["DiscountAmt"],
        healthAmt: json["HealthAmt"] == null ? null : json["HealthAmt"],
        relateNumber: json["RelateNumber"] == null ? null : json["RelateNumber"],
        remark: json["Remark"] == null ? null : json["Remark"],
    );

    Map<String, dynamic> toJson() => {
        "SequenceNo": sequenceNo == null ? null : sequenceNo,
        "ItemName": itemName == null ? null : itemName,
        "Qty": qty == null ? null : qty,
        "Unit": unit == null ? null : unit,
        "UnitPrice": unitPrice == null ? null : unitPrice,
        "SalesAmt": salesAmt == null ? null : salesAmt,
        "TaxAmt": taxAmt == null ? null : taxAmt,
        "TotalAmt": totalAmt == null ? null : totalAmt,
        "DiscountAmt": discountAmt == null ? null : discountAmt,
        "HealthAmt": healthAmt == null ? null : healthAmt,
        "RelateNumber": relateNumber == null ? null : relateNumber,
        "Remark": remark == null ? null : remark,
    };
}
