// To parse this JSON data, do
//
//     final resetPasswordReq = resetPasswordReqFromJson(jsonString);

import 'dart:convert';

class ResetPasswordReq {
  ResetPasswordReq({
    this.oldPassword,
    this.newPassword,
    this.checkPassword,
  });

  String oldPassword;
  String newPassword;
  String checkPassword;

  ResetPasswordReq copyWith({
    String oldPassword,
    String newPassword,
    String checkPassword,
  }) =>
      ResetPasswordReq(
        oldPassword: oldPassword ?? this.oldPassword,
        newPassword: newPassword ?? this.newPassword,
        checkPassword: checkPassword ?? this.checkPassword,
      );

  factory ResetPasswordReq.fromRawJson(String str) =>
      ResetPasswordReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ResetPasswordReq.fromJson(Map<String, dynamic> json) =>
      ResetPasswordReq(
        oldPassword: json["old_password"],
        newPassword: json["new_password"],
        checkPassword: json["check_password"],
      );

  Map<String, dynamic> toJson() => {
        "old_password": oldPassword,
        "new_password": newPassword,
        "check_password": checkPassword,
      };
}
