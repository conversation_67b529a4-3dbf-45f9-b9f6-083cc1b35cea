# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "14.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.41.2"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0-nullsafety.1"
  barcode:
    dependency: "direct main"
    description:
      name: barcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.17.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.1"
  build:
    dependency: transitive
    description:
      name: build
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.5"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.7"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.3"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.7"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.3.2"
  built_value:
    dependency: transitive
    description:
      name: built_value
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.3"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0-nullsafety.3"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.5"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.12"
  dartx:
    dependency: transitive
    description:
      name: dartx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.10"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.2.1"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.11"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.1"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_sunmi_printer:
    dependency: "direct main"
    description:
      path: "."
      ref: "32859e4384e40cb9307eba22440d3fbd3313c19a"
      resolved-ref: "32859e4384e40cb9307eba22440d3fbd3313c19a"
      url: "https://bitbucket.org/umomos/flutter_sunmi_printer.git"
    source: git
    version: "0.2.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.19.1"
  flutter_svg_provider:
    dependency: "direct main"
    description:
      name: flutter_svg_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.8"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.26.0"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.0"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  graphs:
    dependency: transitive
    description:
      name: graphs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  hive:
    dependency: "direct main"
    description:
      name: hive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.4+1"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.1"
  hive_generator:
    dependency: "direct dev"
    description:
      name: hive_generator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.2"
  http:
    dependency: "direct dev"
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.2"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.4"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.19"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.16.1"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  logger:
    dependency: "direct main"
    description:
      name: logger
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.4"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.11.4"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10-nullsafety.1"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0-nullsafety.3"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.7"
  mockito:
    dependency: "direct dev"
    description:
      name: mockito
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.4"
  node_interop:
    dependency: transitive
    description:
      name: node_interop
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  node_io:
    dependency: transitive
    description:
      name: node_io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.3"
  package_info:
    dependency: "direct main"
    description:
      name: package_info
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.3+4"
  path:
    dependency: "direct main"
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0-nullsafety.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.4"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.28"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+2"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.4+8"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.4+3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.2"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.1"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  pool:
    dependency: transitive
    description:
      name: pool
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.0"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.13"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.8"
  qr:
    dependency: transitive
    description:
      name: qr
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.5"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.9"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.4+1"
  sizer:
    dependency: "direct main"
    description:
      name: sizer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.8"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.10+3"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0-nullsafety.2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0-nullsafety.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.1"
  stream_transform:
    dependency: "direct main"
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.19-nullsafety.2"
  time:
    dependency: transitive
    description:
      name: time
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.1"
  timing:
    dependency: transitive
    description:
      name: timing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1+3"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0-nullsafety.3"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.3"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.7+15"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2"
  xml:
    dependency: "direct main"
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.1"
sdks:
  dart: ">=2.10.0 <2.11.0"
  flutter: ">=1.20.0 <2.0.0"
