name: guests
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.1.5+46

environment:
  sdk: ">=2.7.0 <3.0.0"

dependencies:
  hive: ^1.4.4+1
  hive_flutter: ^0.3.1
  flutter_svg_provider: ^0.1.8
  get_storage: ^1.4.0
  jwt_decoder: ^1.0.4
  path_provider: ^1.6.28
  path: ^1.7.0
  package_info: ^0.4.3+4
  dio: ^3.0.10
  intl: ^0.16.1
  flutter_svg: ^0.19.1
  logger: ^0.9.4
  barcode: ^1.17.1
  get: ^3.26.0
  screenshot: ^0.2.0
  encrypt: ^4.1.0
  stream_transform: ^1.2.0
  xml: ^4.5.1
  sizer: ^1.1.8
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0
  flutter_sunmi_printer:
    git:
      url: https://bitbucket.org/umomos/flutter_sunmi_printer.git
      ref: 32859e4384e40cb9307eba22440d3fbd3313c19a

# dependency_overrides:
#   flutter_sunmi_printer:
#     # path: ../flutter_sunmi_printer
#     git:
#       url: https://bitbucket.org/umomos/flutter_sunmi_printer.git
#       ref: dev

dev_dependencies:
  hive_generator: ^0.8.2
  build_runner: ^1.11.1
  flutter_launcher_icons: 0.8.1
  flutter_test:
    sdk: flutter
  mockito: ^4.1.4
  http: ^0.12.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/models/
    - assets/images/
    - assets/icons/
    - assets/data/
    - assets/ca/lets-encrypt-r3.pem
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# https://medium.com/@psyanite/how-to-add-app-launcher-icons-in-flutter-bd92b0e0873a
# https://ab20803.medium.com/flutter-%E5%85%A9%E5%80%8B%E5%B9%B3%E5%8F%B0app-icon%E7%9A%84%E8%A8%AD%E7%BD%AE%E6%96%B9%E5%BC%8F-647e7bc2e680
# flutter packages pub run flutter_launcher_icons:main
flutter_icons:
  ios: true
  android: true
  image_path_ios: "assets/icons/icon.png"
  image_path_android: "assets/icons/ic_launcher.png"
  # adaptive_icon_background: "assets/icons/background.png"
  # adaptive_icon_foreground: "assets/icons/foreground.png"
